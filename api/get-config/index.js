/**
 * @param {import("@azure/functions").Context} context
 * @param {import("@azure/functions").HttpRequest} req
 */
module.exports = async function (context, req) {
    context.log('JavaScript HTTP trigger function processed a request to get config.');

    // Retrieve the configuration values from the application's environment variables.
    // These are set in the Azure Static Web App "Configuration" section.
    const config = {
        AZURE_ENDPOINT: process.env.AZURE_ENDPOINT,
        AZURE_API_KEY: process.env.AZURE_API_KEY,
        DEPLOYMENT_NAME: process.env.DEPLOYMENT_NAME
    };

    // Send the configuration object back to the front-end as the response.
    context.res = {
        status: 200,
        headers: { 'Content-Type': 'application/json' },
        body: config
    };
}

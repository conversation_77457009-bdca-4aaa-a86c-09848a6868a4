<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TravelBot - Your AI Travel Agent</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
        #chat-container::-webkit-scrollbar { width: 8px; }
        #chat-container::-webkit-scrollbar-track { background: #f1f1f1; }
        #chat-container::-webkit-scrollbar-thumb { background: #888; border-radius: 4px; }
    </style>
</head>
<body class="bg-gray-100 flex items-center justify-center h-screen">
    <div class="w-full max-w-2xl h-[90vh] flex flex-col bg-white rounded-xl shadow-2xl border border-gray-200">
        <header class="bg-blue-600 text-white p-4 rounded-t-xl text-center">
            <h1 class="text-xl font-bold">✈️ Chat with TravelBot (CI/CD Deployed)</h1>
        </header>
        <div id="chat-container" class="flex-1 p-6 overflow-y-auto space-y-4">
            <div class="flex justify-start">
                <div class="bg-gray-200 text-gray-800 p-3 rounded-lg max-w-md">
                    <p>Hello! I'm TravelBot. Ask me about destinations to get started! 🌴</p>
                </div>
            </div>
        </div>
        <footer class="p-4 border-t border-gray-200 bg-white rounded-b-xl">
            <div class="flex items-center">
                <input type="text" id="userInput" class="flex-1 p-3 border border-gray-300 rounded-lg" placeholder="Loading configuration..." autocomplete="off" disabled>
                <button id="sendButton" class="ml-3 bg-blue-600 text-white p-3 rounded-lg font-semibold cursor-not-allowed" disabled>
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path></svg>
                </button>
            </div>
        </footer>
    </div>

    <script>
        const sendButton = document.getElementById('sendButton');
        const userInput = document.getElementById('userInput');
        const chatContainer = document.getElementById('chat-container');

        let config = {};
        let conversationHistory = [{
            role: "system",
            content: "You are a friendly and enthusiastic travel agent chatbot named TravelBot. Use only the information provided in your knowledge base to answer questions. If you don't know the answer, politely say so."
        }];

        // 1. Fetch the configuration from our secure API function first
        document.addEventListener('DOMContentLoaded', async () => {
            try {
                const response = await fetch('/api/get-config');
                if (!response.ok) throw new Error('Failed to fetch configuration.');
                
                config = await response.json();

                if (!config.AZURE_ENDPOINT || !config.AZURE_API_KEY || !config.DEPLOYMENT_NAME) {
                    throw new Error("Configuration from server is incomplete.");
                }

                // Enable the chat input now that we have the keys
                userInput.disabled = false;
                sendButton.disabled = false;
                sendButton.classList.remove('cursor-not-allowed');
                userInput.placeholder = "Ask about a destination...";

            } catch (error) {
                console.error("Failed to load configuration:", error);
                appendMessage("Could not connect to the AI service. Please check the application configuration.", 'error');
            }
        });

        const sendMessage = async () => {
            const message = userInput.value.trim();
            if (!message || !config.AZURE_ENDPOINT) return;

            const apiUrl = `${config.AZURE_ENDPOINT}openai/deployments/${config.DEPLOYMENT_NAME}/chat/completions?api-version=2024-02-01`;

            appendMessage(message, 'user');
            conversationHistory.push({ role: "user", content: message });
            userInput.value = '';
            
            const typingIndicator = showTypingIndicator();

            try {
                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'api-key': config.AZURE_API_KEY
                    },
                    body: JSON.stringify({ messages: conversationHistory })
                });

                if (!response.ok) {
                    const errorBody = await response.json();
                    throw new Error(errorBody.error.message || 'An unknown API error occurred.');
                }

                const data = await response.json();
                const agentResponse = data.choices[0].message.content;

                removeTypingIndicator(typingIndicator);
                appendMessage(agentResponse, 'agent');
                conversationHistory.push({ role: "assistant", content: agentResponse });

            } catch (error) {
                console.error("Error calling TravelBot:", error);
                removeTypingIndicator(typingIndicator);
                appendMessage(`Sorry, something went wrong: ${error.message}`, 'error');
            }
        };

        // Helper functions
        function appendMessage(text, sender) {
            const messageWrapper = document.createElement('div');
            const messageBubble = document.createElement('div');
            messageBubble.classList.add('p-3', 'rounded-lg', 'max-w-md');
            if (sender === 'user') {
                messageWrapper.classList.add('flex', 'justify-end');
                messageBubble.classList.add('bg-blue-500', 'text-white');
            } else if (sender === 'agent') {
                messageWrapper.classList.add('flex', 'justify-start');
                messageBubble.classList.add('bg-gray-200', 'text-gray-800');
            } else { // error
                messageWrapper.classList.add('flex', 'justify-start');
                messageBubble.classList.add('bg-red-100', 'text-red-700');
            }
            messageBubble.innerHTML = `<p>${text.replace(/\n/g, '<br>')}</p>`;
            messageWrapper.appendChild(messageBubble);
            chatContainer.appendChild(messageWrapper);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }

        function showTypingIndicator() {
            const indicatorWrapper = document.createElement('div');
            indicatorWrapper.id = 'typing-indicator';
            indicatorWrapper.innerHTML = `<div class="flex justify-start"><div class="bg-gray-200 p-3 rounded-lg"><div class="flex items-center space-x-1"><div class="w-2 h-2 bg-gray-500 rounded-full animate-pulse"></div><div class="w-2 h-2 bg-gray-500 rounded-full animate-pulse" style="animation-delay: 0.2s;"></div><div class="w-2 h-2 bg-gray-500 rounded-full animate-pulse" style="animation-delay: 0.4s;"></div></div></div></div>`;
            chatContainer.appendChild(indicatorWrapper);
            chatContainer.scrollTop = chatContainer.scrollHeight;
            return indicatorWrapper;
        }

        function removeTypingIndicator(indicator) {
            if (indicator) chatContainer.removeChild(indicator);
        }

        sendButton.addEventListener('click', sendMessage);
        userInput.addEventListener('keydown', (event) => {
            if (event.key === 'Enter') sendMessage();
        });
    </script>
</body>
</html>
